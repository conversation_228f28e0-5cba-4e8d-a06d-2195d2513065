import os
from dotenv import load_dotenv
from pydantic import BaseSettings
from typing import Optional

load_dotenv()

class Settings(BaseSettings):
    # Telegram Configuration
    telegram_bot_token: str = os.getenv("TELEGRAM_BOT_TOKEN", "")
    
    # xAI Grok Configuration
    xai_api_key: str = os.getenv("XAI_API_KEY", "")
    xai_base_url: str = os.getenv("XAI_BASE_URL", "https://api.x.ai/v1")
    
    # Hugging Face Configuration
    huggingface_api_token: str = os.getenv("HUGGINGFACE_API_TOKEN", "")
    
    # Social Media Configuration
    linkedin_client_id: str = os.getenv("LINKEDIN_CLIENT_ID", "")
    linkedin_client_secret: str = os.getenv("LINKEDIN_CLIENT_SECRET", "")
    linkedin_access_token: str = os.getenv("LINKEDIN_ACCESS_TOKEN", "")
    
    facebook_access_token: str = os.getenv("FACEBOOK_ACCESS_TOKEN", "")
    facebook_page_id: str = os.getenv("FACEBOOK_PAGE_ID", "")
    
    instagram_username: str = os.getenv("INSTAGRAM_USERNAME", "")
    instagram_password: str = os.getenv("INSTAGRAM_PASSWORD", "")
    
    # Application Settings
    debug: bool = os.getenv("DEBUG", "True").lower() == "true"
    log_level: str = os.getenv("LOG_LEVEL", "INFO")
    max_crawl_pages: int = int(os.getenv("MAX_CRAWL_PAGES", "5"))
    image_generation_model: str = os.getenv("IMAGE_GENERATION_MODEL", "stabilityai/stable-diffusion-2-1")
    
    class Config:
        env_file = ".env"

settings = Settings()

# Validation
def validate_config():
    """Validate that required configuration is present"""
    required_fields = [
        ("telegram_bot_token", "Telegram Bot Token"),
        ("xai_api_key", "xAI API Key"),
        ("huggingface_api_token", "Hugging Face API Token")
    ]
    
    missing_fields = []
    for field, description in required_fields:
        if not getattr(settings, field):
            missing_fields.append(description)
    
    if missing_fields:
        raise ValueError(f"Missing required configuration: {', '.join(missing_fields)}")
    
    return True
