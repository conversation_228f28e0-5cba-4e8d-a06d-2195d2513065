import asyncio
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, MessageHandler, CallbackQueryHandler, ContextTypes, filters
from loguru import logger
from config import settings
import json
import io
from typing import Dict, List
import base64

from web_crawler import WebCrawler
from ai_content_generator import AIContentGenerator
from image_generator import ImageGenerator
from social_media_poster import SocialMediaPoster

class TelegramBot:
    def __init__(self):
        self.token = settings.telegram_bot_token
        self.crawler = WebCrawler()
        self.ai_generator = AIContentGenerator()
        self.image_generator = ImageGenerator()
        self.social_poster = SocialMediaPoster()
        
        # Store user sessions
        self.user_sessions = {}
        
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command"""
        welcome_message = """
🚀 Welcome to K-Poster Bot!

I can help you create engaging social media posts by:
1. 📊 Researching trending topics
2. 🤖 Generating AI-powered content
3. 🎨 Creating custom images
4. 📱 Posting to multiple platforms

To get started, just send me a topic you want to post about!

Example: "artificial intelligence trends"
        """
        
        await update.message.reply_text(welcome_message)
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        help_message = """
📖 How to use K-Poster Bot:

1. Send me any topic (e.g., "blockchain technology")
2. I'll research the topic from multiple sources
3. AI will generate engaging content and image
4. You can preview and approve the content
5. Choose platforms to post (LinkedIn, Facebook, Instagram)
6. I'll post it for you!

Commands:
/start - Start the bot
/help - Show this help message
/cancel - Cancel current operation

Just send me a topic to begin! 🚀
        """
        
        await update.message.reply_text(help_message)
    
    async def cancel_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /cancel command"""
        user_id = update.effective_user.id
        if user_id in self.user_sessions:
            del self.user_sessions[user_id]
        
        await update.message.reply_text("❌ Operation cancelled. Send me a new topic to start over!")
    
    async def handle_topic_input(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle user topic input"""
        user_id = update.effective_user.id
        topic = update.message.text.strip()
        
        if len(topic) < 3:
            await update.message.reply_text("Please provide a more specific topic (at least 3 characters).")
            return
        
        # Initialize user session
        self.user_sessions[user_id] = {
            "topic": topic,
            "step": "processing"
        }
        
        # Send processing message
        processing_msg = await update.message.reply_text(
            f"🔍 Processing your request for: *{topic}*\n\n"
            "⏳ This may take a few minutes...\n"
            "📊 Crawling sources...",
            parse_mode="Markdown"
        )
        
        try:
            # Step 1: Crawl content
            await processing_msg.edit_text(
                f"🔍 Processing: *{topic}*\n\n"
                "📊 Crawling sources... ✅\n"
                "🤖 Generating content...",
                parse_mode="Markdown"
            )
            
            articles = self.crawler.crawl_all_sources(topic)
            
            if not articles:
                await processing_msg.edit_text(
                    "❌ Sorry, I couldn't find enough content about this topic. Please try a different topic."
                )
                return
            
            # Step 2: Generate content
            await processing_msg.edit_text(
                f"🔍 Processing: *{topic}*\n\n"
                "📊 Crawling sources... ✅\n"
                "🤖 Generating content... ✅\n"
                "🎨 Creating image...",
                parse_mode="Markdown"
            )
            
            content_summary = self.ai_generator.generate_content_summary(articles, topic)
            
            # Step 3: Generate image
            image_description = self.ai_generator.generate_image_description(content_summary, topic)
            image_bytes = self.image_generator.generate_image(image_description)
            
            await processing_msg.edit_text(
                f"🔍 Processing: *{topic}*\n\n"
                "📊 Crawling sources... ✅\n"
                "🤖 Generating content... ✅\n"
                "🎨 Creating image... ✅\n"
                "📝 Preparing preview...",
                parse_mode="Markdown"
            )
            
            # Store in session
            self.user_sessions[user_id].update({
                "articles": articles,
                "content": content_summary,
                "image_bytes": image_bytes,
                "step": "preview"
            })
            
            # Send preview
            await self.send_content_preview(update, user_id)
            await processing_msg.delete()
            
        except Exception as e:
            logger.error(f"Error processing topic {topic}: {e}")
            await processing_msg.edit_text(
                f"❌ Sorry, there was an error processing your request: {str(e)}\n\n"
                "Please try again with a different topic."
            )
    
    async def send_content_preview(self, update: Update, user_id: int):
        """Send content preview to user"""
        session = self.user_sessions.get(user_id)
        if not session:
            return
        
        content = session["content"]
        image_bytes = session.get("image_bytes")
        
        # Prepare preview text
        preview_text = f"""
📝 *Content Preview*

*Short Post:*
{content.get('short_post', 'N/A')}

*Long Post:*
{content.get('long_post', 'N/A')}

*Hashtags:*
{' '.join([f"#{tag}" for tag in content.get('hashtags', [])])}

*Key Insights:*
{chr(10).join([f"• {insight}" for insight in content.get('key_insights', [])])}
        """
        
        # Create approval buttons
        keyboard = [
            [
                InlineKeyboardButton("✅ Approve", callback_data="approve_content"),
                InlineKeyboardButton("🔄 Regenerate", callback_data="regenerate_content")
            ],
            [InlineKeyboardButton("❌ Cancel", callback_data="cancel_operation")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        # Send image if available
        if image_bytes:
            try:
                await update.effective_chat.send_photo(
                    photo=io.BytesIO(image_bytes),
                    caption=preview_text,
                    parse_mode="Markdown",
                    reply_markup=reply_markup
                )
            except Exception as e:
                logger.error(f"Error sending image: {e}")
                await update.effective_chat.send_message(
                    text=preview_text + "\n\n⚠️ Image generation failed, but content is ready!",
                    parse_mode="Markdown",
                    reply_markup=reply_markup
                )
        else:
            await update.effective_chat.send_message(
                text=preview_text + "\n\n⚠️ Image generation failed, but content is ready!",
                parse_mode="Markdown",
                reply_markup=reply_markup
            )
    
    async def handle_callback_query(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle button callbacks"""
        query = update.callback_query
        await query.answer()
        
        user_id = update.effective_user.id
        session = self.user_sessions.get(user_id)
        
        if not session:
            await query.edit_message_text("❌ Session expired. Please start over with a new topic.")
            return
        
        if query.data == "approve_content":
            await self.show_platform_selection(query, user_id)
            
        elif query.data == "regenerate_content":
            await query.edit_message_text("🔄 Regenerating content...")
            # Regenerate content with same articles
            topic = session["topic"]
            articles = session["articles"]
            
            try:
                new_content = self.ai_generator.generate_content_summary(articles, topic)
                new_image_description = self.ai_generator.generate_image_description(new_content, topic)
                new_image_bytes = self.image_generator.generate_image(new_image_description)
                
                session.update({
                    "content": new_content,
                    "image_bytes": new_image_bytes
                })
                
                await self.send_content_preview(update, user_id)
                
            except Exception as e:
                await query.edit_message_text(f"❌ Error regenerating content: {str(e)}")
                
        elif query.data == "cancel_operation":
            if user_id in self.user_sessions:
                del self.user_sessions[user_id]
            await query.edit_message_text("❌ Operation cancelled.")
            
        elif query.data.startswith("platform_"):
            await self.handle_platform_selection(query, user_id)
            
        elif query.data == "post_to_selected":
            await self.post_to_selected_platforms(query, user_id)
    
    async def show_platform_selection(self, query, user_id: int):
        """Show platform selection buttons"""
        session = self.user_sessions.get(user_id)
        if not session:
            return
        
        session["selected_platforms"] = []
        session["step"] = "platform_selection"
        
        keyboard = [
            [
                InlineKeyboardButton("📘 LinkedIn", callback_data="platform_linkedin"),
                InlineKeyboardButton("📘 Facebook", callback_data="platform_facebook")
            ],
            [
                InlineKeyboardButton("📷 Instagram", callback_data="platform_instagram")
            ],
            [
                InlineKeyboardButton("✅ Post to Selected", callback_data="post_to_selected"),
                InlineKeyboardButton("❌ Cancel", callback_data="cancel_operation")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(
            "📱 *Select platforms to post to:*\n\n"
            "Click on the platforms where you want to post your content.\n"
            "Then click 'Post to Selected' when ready.",
            parse_mode="Markdown",
            reply_markup=reply_markup
        )
    
    async def handle_platform_selection(self, query, user_id: int):
        """Handle platform selection"""
        session = self.user_sessions.get(user_id)
        if not session:
            return
        
        platform = query.data.replace("platform_", "")
        selected_platforms = session.get("selected_platforms", [])
        
        if platform in selected_platforms:
            selected_platforms.remove(platform)
        else:
            selected_platforms.append(platform)
        
        session["selected_platforms"] = selected_platforms
        
        # Update button text to show selection
        platform_emojis = {
            "linkedin": "📘",
            "facebook": "📘", 
            "instagram": "📷"
        }
        
        keyboard = []
        for p in ["linkedin", "facebook"]:
            emoji = platform_emojis[p]
            text = f"{emoji} {p.title()}"
            if p in selected_platforms:
                text += " ✅"
            keyboard.append(InlineKeyboardButton(text, callback_data=f"platform_{p}"))
        
        keyboard = [keyboard]  # First row
        
        # Instagram row
        instagram_text = f"{platform_emojis['instagram']} Instagram"
        if "instagram" in selected_platforms:
            instagram_text += " ✅"
        keyboard.append([InlineKeyboardButton(instagram_text, callback_data="platform_instagram")])
        
        # Action buttons
        keyboard.append([
            InlineKeyboardButton("✅ Post to Selected", callback_data="post_to_selected"),
            InlineKeyboardButton("❌ Cancel", callback_data="cancel_operation")
        ])
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        selected_text = ", ".join(selected_platforms) if selected_platforms else "None"
        
        await query.edit_message_text(
            f"📱 *Select platforms to post to:*\n\n"
            f"*Selected:* {selected_text}\n\n"
            "Click on platforms to toggle selection.\n"
            "Then click 'Post to Selected' when ready.",
            parse_mode="Markdown",
            reply_markup=reply_markup
        )
    
    async def post_to_selected_platforms(self, query, user_id: int):
        """Post content to selected platforms"""
        session = self.user_sessions.get(user_id)
        if not session:
            return
        
        selected_platforms = session.get("selected_platforms", [])
        if not selected_platforms:
            await query.edit_message_text("❌ Please select at least one platform.")
            return
        
        content = session["content"]
        image_bytes = session.get("image_bytes")
        
        await query.edit_message_text(
            f"📤 Posting to {', '.join(selected_platforms)}...\n\n"
            "⏳ Please wait..."
        )
        
        try:
            results = {}
            for platform in selected_platforms:
                # Enhance content for specific platform
                platform_content = self.ai_generator.enhance_content_for_platform(content, platform)
                
                # Resize image for platform if needed
                platform_image = None
                if image_bytes:
                    platform_image = self.image_generator.resize_image_for_platform(image_bytes, platform)
                
                # Post to platform
                result = self.social_poster.post_to_platform(platform, platform_content, platform_image)
                results[platform] = result
            
            # Send results
            await self.send_posting_results(query, results)
            
        except Exception as e:
            logger.error(f"Error posting to platforms: {e}")
            await query.edit_message_text(f"❌ Error posting to platforms: {str(e)}")
    
    async def send_posting_results(self, query, results: Dict):
        """Send posting results to user"""
        result_text = "📊 *Posting Results:*\n\n"
        
        for platform, result in results.items():
            if result.get("success"):
                result_text += f"✅ {platform.title()}: Posted successfully\n"
            else:
                error = result.get("error", "Unknown error")
                result_text += f"❌ {platform.title()}: Failed - {error}\n"
        
        result_text += "\n🎉 Thank you for using K-Poster Bot!\n"
        result_text += "Send me another topic to create more posts!"
        
        await query.edit_message_text(result_text, parse_mode="Markdown")
    
    def run(self):
        """Run the Telegram bot"""
        logger.info("Starting Telegram bot...")
        
        # Create application
        application = Application.builder().token(self.token).build()
        
        # Add handlers
        application.add_handler(CommandHandler("start", self.start_command))
        application.add_handler(CommandHandler("help", self.help_command))
        application.add_handler(CommandHandler("cancel", self.cancel_command))
        application.add_handler(CallbackQueryHandler(self.handle_callback_query))
        application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_topic_input))
        
        # Run the bot
        logger.info("Bot is running...")
        application.run_polling()
