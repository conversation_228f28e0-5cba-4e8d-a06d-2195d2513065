#!/usr/bin/env python3
"""
K-Poster: AI-Powered Social Media Content Generator

This application creates engaging social media posts by:
1. Taking topic input from Telegram
2. Crawling content from multiple sources (Medium, Quora, Reddit)
3. Using AI (Grok) to generate summaries and image descriptions
4. Creating AI-generated images
5. Posting to multiple social media platforms

Author: K-Poster Team
Version: 1.0.0
"""

import asyncio
import sys
from loguru import logger
from config import settings, validate_config
from telegram_bot import TelegramBot

def setup_logging():
    """Setup logging configuration"""
    logger.remove()  # Remove default handler
    
    # Add console handler
    logger.add(
        sys.stdout,
        level=settings.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        colorize=True
    )
    
    # Add file handler
    logger.add(
        "logs/k_poster.log",
        level="DEBUG",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB",
        retention="7 days",
        compression="zip"
    )

def create_directories():
    """Create necessary directories"""
    import os
    
    directories = [
        "logs",
        "generated_images",
        "temp"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"Created directory: {directory}")

def check_dependencies():
    """Check if all required dependencies are available"""
    try:
        import telegram
        import requests
        import beautifulsoup4
        import openai
        import PIL
        logger.info("All required dependencies are available")
        return True
    except ImportError as e:
        logger.error(f"Missing dependency: {e}")
        logger.error("Please install dependencies with: pip install -r requirements.txt")
        return False

def main():
    """Main application entry point"""
    logger.info("🚀 Starting K-Poster Application")
    
    try:
        # Setup
        setup_logging()
        create_directories()
        
        # Check dependencies
        if not check_dependencies():
            logger.error("❌ Dependency check failed")
            sys.exit(1)
        
        # Validate configuration
        logger.info("🔧 Validating configuration...")
        validate_config()
        logger.info("✅ Configuration validated successfully")
        
        # Initialize and run Telegram bot
        logger.info("🤖 Initializing Telegram bot...")
        bot = TelegramBot()
        
        logger.info("✅ K-Poster is ready!")
        logger.info("📱 Send a message to your Telegram bot to start creating posts!")
        
        # Run the bot
        bot.run()
        
    except KeyboardInterrupt:
        logger.info("👋 K-Poster stopped by user")
        sys.exit(0)
        
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
