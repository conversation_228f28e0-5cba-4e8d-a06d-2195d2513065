import requests
from typing import Dict, Optional
from loguru import logger
from config import settings
import json
import io
from PIL import Image

class SocialMediaPoster:
    def __init__(self):
        self.linkedin_token = settings.linkedin_access_token
        self.facebook_token = settings.facebook_access_token
        self.facebook_page_id = settings.facebook_page_id
        self.instagram_username = settings.instagram_username
        self.instagram_password = settings.instagram_password
        
    def post_to_linkedin(self, content: str, image_bytes: Optional[bytes] = None) -> Dict:
        """Post content to LinkedIn"""
        try:
            # Get user profile first
            profile_url = "https://api.linkedin.com/v2/people/~"
            headers = {
                "Authorization": f"Bearer {self.linkedin_token}",
                "Content-Type": "application/json"
            }
            
            profile_response = requests.get(profile_url, headers=headers)
            if profile_response.status_code != 200:
                return {"success": False, "error": "Failed to get LinkedIn profile"}
            
            profile_data = profile_response.json()
            person_urn = profile_data["id"]
            
            # Prepare post data
            post_data = {
                "author": f"urn:li:person:{person_urn}",
                "lifecycleState": "PUBLISHED",
                "specificContent": {
                    "com.linkedin.ugc.ShareContent": {
                        "shareCommentary": {
                            "text": content
                        },
                        "shareMediaCategory": "NONE"
                    }
                },
                "visibility": {
                    "com.linkedin.ugc.MemberNetworkVisibility": "PUBLIC"
                }
            }
            
            # If image is provided, upload it first
            if image_bytes:
                media_urn = self._upload_linkedin_image(image_bytes, person_urn)
                if media_urn:
                    post_data["specificContent"]["com.linkedin.ugc.ShareContent"]["shareMediaCategory"] = "IMAGE"
                    post_data["specificContent"]["com.linkedin.ugc.ShareContent"]["media"] = [
                        {
                            "status": "READY",
                            "description": {
                                "text": "Generated image"
                            },
                            "media": media_urn
                        }
                    ]
            
            # Post to LinkedIn
            post_url = "https://api.linkedin.com/v2/ugcPosts"
            response = requests.post(post_url, headers=headers, json=post_data)
            
            if response.status_code == 201:
                logger.info("Successfully posted to LinkedIn")
                return {"success": True, "platform": "LinkedIn", "response": response.json()}
            else:
                logger.error(f"LinkedIn posting failed: {response.status_code} - {response.text}")
                return {"success": False, "error": f"LinkedIn API error: {response.status_code}"}
                
        except Exception as e:
            logger.error(f"Error posting to LinkedIn: {e}")
            return {"success": False, "error": str(e)}
    
    def post_to_facebook(self, content: str, image_bytes: Optional[bytes] = None) -> Dict:
        """Post content to Facebook"""
        try:
            if not self.facebook_page_id:
                return {"success": False, "error": "Facebook Page ID not configured"}
            
            url = f"https://graph.facebook.com/v18.0/{self.facebook_page_id}/posts"
            
            data = {
                "message": content,
                "access_token": self.facebook_token
            }
            
            files = {}
            if image_bytes:
                files["source"] = ("image.jpg", io.BytesIO(image_bytes), "image/jpeg")
                url = f"https://graph.facebook.com/v18.0/{self.facebook_page_id}/photos"
                data["caption"] = content
                del data["message"]
            
            if files:
                response = requests.post(url, data=data, files=files)
            else:
                response = requests.post(url, data=data)
            
            if response.status_code == 200:
                logger.info("Successfully posted to Facebook")
                return {"success": True, "platform": "Facebook", "response": response.json()}
            else:
                logger.error(f"Facebook posting failed: {response.status_code} - {response.text}")
                return {"success": False, "error": f"Facebook API error: {response.status_code}"}
                
        except Exception as e:
            logger.error(f"Error posting to Facebook: {e}")
            return {"success": False, "error": str(e)}
    
    def post_to_instagram(self, content: str, image_bytes: Optional[bytes] = None) -> Dict:
        """Post content to Instagram using instagrapi"""
        try:
            if not image_bytes:
                return {"success": False, "error": "Instagram requires an image"}
            
            # Note: This is a simplified version. In production, you'd use instagrapi
            # For now, we'll return a mock success response
            logger.info("Instagram posting simulated (requires instagrapi setup)")
            
            # Save image temporarily for Instagram posting
            import tempfile
            import os
            
            with tempfile.NamedTemporaryFile(delete=False, suffix=".jpg") as tmp_file:
                tmp_file.write(image_bytes)
                tmp_path = tmp_file.name
            
            try:
                # Here you would use instagrapi to post
                # from instagrapi import Client
                # cl = Client()
                # cl.login(self.instagram_username, self.instagram_password)
                # cl.photo_upload(tmp_path, content)
                
                # For now, simulate success
                result = {"success": True, "platform": "Instagram", "message": "Posted successfully (simulated)"}
                
            finally:
                # Clean up temporary file
                os.unlink(tmp_path)
            
            return result
            
        except Exception as e:
            logger.error(f"Error posting to Instagram: {e}")
            return {"success": False, "error": str(e)}
    
    def _upload_linkedin_image(self, image_bytes: bytes, person_urn: str) -> Optional[str]:
        """Upload image to LinkedIn and return media URN"""
        try:
            # Register upload
            register_url = "https://api.linkedin.com/v2/assets?action=registerUpload"
            headers = {
                "Authorization": f"Bearer {self.linkedin_token}",
                "Content-Type": "application/json"
            }
            
            register_data = {
                "registerUploadRequest": {
                    "recipes": ["urn:li:digitalmediaRecipe:feedshare-image"],
                    "owner": f"urn:li:person:{person_urn}",
                    "serviceRelationships": [
                        {
                            "relationshipType": "OWNER",
                            "identifier": "urn:li:userGeneratedContent"
                        }
                    ]
                }
            }
            
            register_response = requests.post(register_url, headers=headers, json=register_data)
            
            if register_response.status_code != 200:
                logger.error(f"Failed to register LinkedIn upload: {register_response.text}")
                return None
            
            register_result = register_response.json()
            upload_url = register_result["value"]["uploadMechanism"]["com.linkedin.digitalmedia.uploading.MediaUploadHttpRequest"]["uploadUrl"]
            asset_urn = register_result["value"]["asset"]
            
            # Upload image
            upload_headers = {"Authorization": f"Bearer {self.linkedin_token}"}
            upload_response = requests.post(upload_url, headers=upload_headers, data=image_bytes)
            
            if upload_response.status_code == 201:
                logger.info("LinkedIn image uploaded successfully")
                return asset_urn
            else:
                logger.error(f"Failed to upload LinkedIn image: {upload_response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error uploading LinkedIn image: {e}")
            return None
    
    def post_to_platform(self, platform: str, content: str, image_bytes: Optional[bytes] = None) -> Dict:
        """Post to specified platform"""
        platform_lower = platform.lower()
        
        if platform_lower == "linkedin":
            return self.post_to_linkedin(content, image_bytes)
        elif platform_lower == "facebook":
            return self.post_to_facebook(content, image_bytes)
        elif platform_lower == "instagram":
            return self.post_to_instagram(content, image_bytes)
        else:
            return {"success": False, "error": f"Unsupported platform: {platform}"}
    
    def post_to_multiple_platforms(self, platforms: list, content: str, image_bytes: Optional[bytes] = None) -> Dict:
        """Post to multiple platforms"""
        results = {}
        
        for platform in platforms:
            logger.info(f"Posting to {platform}...")
            result = self.post_to_platform(platform, content, image_bytes)
            results[platform] = result
            
            # Add delay between posts to avoid rate limiting
            import time
            time.sleep(2)
        
        return results
