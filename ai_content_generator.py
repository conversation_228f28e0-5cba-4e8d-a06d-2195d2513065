import openai
from typing import List, Dict, Tuple
from loguru import logger
from config import settings
import json

class AIContentGenerator:
    def __init__(self):
        # Configure xAI Grok API
        self.client = openai.OpenAI(
            api_key=settings.xai_api_key,
            base_url=settings.xai_base_url
        )
        
    def generate_content_summary(self, articles: List[Dict], topic: str) -> str:
        """Generate a summarized post from crawled articles"""
        try:
            # Prepare content for summarization
            content_text = self._prepare_content_for_summary(articles, topic)
            
            prompt = f"""
            Based on the following articles about "{topic}", create an engaging social media post that:
            1. Summarizes the key insights and trends
            2. Is informative yet engaging
            3. Includes relevant hashtags
            4. Is suitable for LinkedIn, Facebook, and Instagram
            5. Keep it under 280 characters for the main content
            6. Add a longer description (up to 500 characters) for detailed platforms
            
            Articles content:
            {content_text}
            
            Please provide the response in JSON format:
            {{
                "short_post": "Brief engaging post under 280 chars",
                "long_post": "Detailed post up to 500 chars",
                "hashtags": ["hashtag1", "hashtag2", "hashtag3"],
                "key_insights": ["insight1", "insight2", "insight3"]
            }}
            """
            
            response = self.client.chat.completions.create(
                model="grok-beta",
                messages=[
                    {"role": "system", "content": "You are an expert social media content creator who creates engaging posts from research articles."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1000,
                temperature=0.7
            )
            
            content = response.choices[0].message.content
            
            # Try to parse JSON response
            try:
                parsed_content = json.loads(content)
                return parsed_content
            except json.JSONDecodeError:
                # Fallback if JSON parsing fails
                return {
                    "short_post": content[:280],
                    "long_post": content[:500],
                    "hashtags": [f"#{topic.replace(' ', '').lower()}", "#trending", "#insights"],
                    "key_insights": ["Generated from multiple sources", "AI-curated content", "Latest trends"]
                }
                
        except Exception as e:
            logger.error(f"Error generating content summary: {e}")
            return self._fallback_content(topic)
    
    def generate_image_description(self, content_summary: Dict, topic: str) -> str:
        """Generate image description for AI image generation"""
        try:
            prompt = f"""
            Based on this social media post about "{topic}", create a detailed image description for AI image generation.
            
            Post content: {content_summary.get('short_post', '')}
            Key insights: {', '.join(content_summary.get('key_insights', []))}
            
            Create a description for a professional, eye-catching image that would complement this post.
            The image should be:
            1. Professional and modern
            2. Relevant to the topic
            3. Suitable for social media
            4. Visually appealing
            5. Not include any text or words
            
            Provide only the image description, no additional text.
            """
            
            response = self.client.chat.completions.create(
                model="grok-beta",
                messages=[
                    {"role": "system", "content": "You are an expert at creating image descriptions for AI image generation tools."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=200,
                temperature=0.8
            )
            
            image_description = response.choices[0].message.content.strip()
            logger.info(f"Generated image description: {image_description}")
            return image_description
            
        except Exception as e:
            logger.error(f"Error generating image description: {e}")
            return f"Professional modern illustration related to {topic}, clean design, vibrant colors, no text"
    
    def _prepare_content_for_summary(self, articles: List[Dict], topic: str) -> str:
        """Prepare article content for summarization"""
        content_parts = []
        
        for i, article in enumerate(articles[:5]):  # Limit to 5 articles
            content_part = f"""
            Article {i+1} from {article.get('source', 'Unknown')}:
            Title: {article.get('title', 'No title')}
            Content: {article.get('content', '')[:500]}...
            """
            content_parts.append(content_part)
        
        return "\n\n".join(content_parts)
    
    def _fallback_content(self, topic: str) -> Dict:
        """Fallback content when AI generation fails"""
        return {
            "short_post": f"Exploring the latest trends in {topic}. Stay tuned for insights and updates! 🚀",
            "long_post": f"Diving deep into {topic} trends. Our research shows exciting developments in this space. From emerging technologies to market shifts, there's a lot happening. What are your thoughts on the current state of {topic}?",
            "hashtags": [f"#{topic.replace(' ', '').lower()}", "#trending", "#insights", "#technology"],
            "key_insights": [
                f"Growing interest in {topic}",
                "Multiple perspectives available",
                "Continuous evolution in the field"
            ]
        }
    
    def enhance_content_for_platform(self, content: Dict, platform: str) -> str:
        """Enhance content for specific social media platform"""
        try:
            platform_prompts = {
                "linkedin": "Make this more professional and business-focused",
                "facebook": "Make this more casual and engaging for general audience",
                "instagram": "Make this more visual and hashtag-friendly"
            }
            
            base_content = content.get('long_post', content.get('short_post', ''))
            hashtags = ' '.join([f"#{tag}" for tag in content.get('hashtags', [])])
            
            prompt = f"""
            Adapt this social media post for {platform}:
            
            Original post: {base_content}
            Hashtags: {hashtags}
            
            {platform_prompts.get(platform, 'Optimize for the platform')}
            
            Keep the core message but adjust tone and style for {platform}.
            """
            
            response = self.client.chat.completions.create(
                model="grok-beta",
                messages=[
                    {"role": "system", "content": f"You are a {platform} content specialist."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=300,
                temperature=0.6
            )
            
            enhanced_content = response.choices[0].message.content.strip()
            return enhanced_content
            
        except Exception as e:
            logger.error(f"Error enhancing content for {platform}: {e}")
            # Fallback to original content
            base_content = content.get('long_post', content.get('short_post', ''))
            hashtags = ' '.join([f"#{tag}" for tag in content.get('hashtags', [])])
            return f"{base_content}\n\n{hashtags}"
