#!/usr/bin/env python3
"""
Test script to verify K-Poster setup and functionality
"""

import sys
import os
from loguru import logger

def test_imports():
    """Test if all required modules can be imported"""
    print("🧪 Testing imports...")
    
    try:
        import telegram
        print("✅ python-telegram-bot")
    except ImportError:
        print("❌ python-telegram-bot not found")
        return False
    
    try:
        import requests
        print("✅ requests")
    except ImportError:
        print("❌ requests not found")
        return False
    
    try:
        from bs4 import BeautifulSoup
        print("✅ beautifulsoup4")
    except ImportError:
        print("❌ beautifulsoup4 not found")
        return False
    
    try:
        import openai
        print("✅ openai")
    except ImportError:
        print("❌ openai not found")
        return False
    
    try:
        from PIL import Image
        print("✅ Pillow")
    except ImportError:
        print("❌ Pillow not found")
        return False
    
    return True

def test_config():
    """Test configuration loading"""
    print("\n🧪 Testing configuration...")
    
    try:
        from config import settings, validate_config
        print("✅ Config module loaded")
        
        # Test basic settings
        if hasattr(settings, 'telegram_bot_token'):
            print("✅ Settings structure valid")
        else:
            print("❌ Settings structure invalid")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Config error: {e}")
        return False

def test_modules():
    """Test individual modules"""
    print("\n🧪 Testing modules...")
    
    try:
        from web_crawler import WebCrawler
        crawler = WebCrawler()
        print("✅ WebCrawler module")
    except Exception as e:
        print(f"❌ WebCrawler error: {e}")
        return False
    
    try:
        from ai_content_generator import AIContentGenerator
        print("✅ AIContentGenerator module")
    except Exception as e:
        print(f"❌ AIContentGenerator error: {e}")
        return False
    
    try:
        from image_generator import ImageGenerator
        print("✅ ImageGenerator module")
    except Exception as e:
        print(f"❌ ImageGenerator error: {e}")
        return False
    
    try:
        from social_media_poster import SocialMediaPoster
        print("✅ SocialMediaPoster module")
    except Exception as e:
        print(f"❌ SocialMediaPoster error: {e}")
        return False
    
    try:
        from telegram_bot import TelegramBot
        print("✅ TelegramBot module")
    except Exception as e:
        print(f"❌ TelegramBot error: {e}")
        return False
    
    return True

def test_directories():
    """Test if required directories exist"""
    print("\n🧪 Testing directories...")
    
    required_dirs = ["logs", "generated_images", "temp"]
    
    for directory in required_dirs:
        if os.path.exists(directory):
            print(f"✅ {directory}/")
        else:
            print(f"❌ {directory}/ missing")
            return False
    
    return True

def test_env_file():
    """Test environment file"""
    print("\n🧪 Testing environment file...")
    
    if not os.path.exists(".env"):
        print("❌ .env file not found")
        return False
    
    print("✅ .env file exists")
    
    # Check if it's not just the example
    with open(".env", "r") as f:
        content = f.read()
    
    if "your_telegram_bot_token_here" in content:
        print("⚠️  .env file contains example values - please configure with real API keys")
        return False
    
    print("✅ .env file appears to be configured")
    return True

def run_basic_functionality_test():
    """Run a basic functionality test"""
    print("\n🧪 Testing basic functionality...")
    
    try:
        # Test web crawler with a simple request
        from web_crawler import WebCrawler
        crawler = WebCrawler()
        print("✅ WebCrawler initialized")
        
        # Test AI content generator initialization
        from ai_content_generator import AIContentGenerator
        # Don't initialize if no API key to avoid errors
        print("✅ AIContentGenerator module ready")
        
        # Test image generator initialization
        from image_generator import ImageGenerator
        # Don't initialize if no API key to avoid errors
        print("✅ ImageGenerator module ready")
        
        return True
        
    except Exception as e:
        print(f"❌ Functionality test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 K-Poster Setup Test")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Configuration Test", test_config),
        ("Module Test", test_modules),
        ("Directory Test", test_directories),
        ("Environment Test", test_env_file),
        ("Functionality Test", run_basic_functionality_test)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} PASSED")
        else:
            print(f"❌ {test_name} FAILED")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your K-Poster setup is ready!")
        print("\nTo start the application:")
        print("python main.py")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        print("\nCommon fixes:")
        print("- Run: pip install -r requirements.txt")
        print("- Configure your .env file with real API keys")
        print("- Run: python setup.py")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
