# K-Poster: AI-Powered Social Media Content Generator

K-Poster is a comprehensive Python application that automates social media content creation using AI. It integrates with Telegram for user interaction, crawls multiple content sources, uses AI for content generation, creates custom images, and posts to multiple social media platforms.

## 🚀 Features

- **Telegram Bot Interface**: Easy-to-use chat interface for content requests
- **Multi-Source Content Crawling**: Automatically gathers content from Medium, Quora, and Reddit
- **AI-Powered Content Generation**: Uses Grok API for intelligent summarization and content creation
- **AI Image Generation**: Creates custom images using Hugging Face's Stable Diffusion models
- **Multi-Platform Posting**: Supports LinkedIn, Facebook, and Instagram
- **Content Preview & Approval**: Users can review and approve content before posting
- **Platform-Specific Optimization**: Tailors content and images for each platform

## 📋 Prerequisites

- Python 3.8 or higher
- Telegram Bot <PERSON>ken (from @BotFather)
- xAI Grok API Key
- Hugging Face API Token (free tier available)
- Social Media API credentials (LinkedIn, Facebook, Instagram)

## 🛠️ Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd K-poster
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Setup environment variables:**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` file with your API keys and credentials:
   ```env
   TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
   XAI_API_KEY=your_xai_api_key_here
   HUGGINGFACE_API_TOKEN=your_huggingface_token_here
   # ... add other credentials
   ```

## 🔑 API Setup Guide

### 1. Telegram Bot
1. Message @BotFather on Telegram
2. Create a new bot with `/newbot`
3. Copy the bot token to your `.env` file

### 2. xAI Grok API
1. Visit [xAI API](https://x.ai/api)
2. Sign up and get your API key
3. Add to `.env` file

### 3. Hugging Face (Free)
1. Sign up at [Hugging Face](https://huggingface.co)
2. Go to Settings > Access Tokens
3. Create a new token and add to `.env`

### 4. Social Media APIs

#### LinkedIn
1. Create a LinkedIn App at [LinkedIn Developers](https://developer.linkedin.com)
2. Get Client ID, Client Secret, and Access Token
3. Add to `.env` file

#### Facebook
1. Create a Facebook App at [Facebook Developers](https://developers.facebook.com)
2. Get Page Access Token and Page ID
3. Add to `.env` file

#### Instagram
1. Use Instagram username and password (or Instagram Basic Display API)
2. Add credentials to `.env` file

## 🚀 Usage

1. **Start the application:**
   ```bash
   python main.py
   ```

2. **Interact with your Telegram bot:**
   - Send `/start` to begin
   - Send any topic (e.g., "artificial intelligence trends")
   - Review the generated content and image
   - Select platforms to post to
   - Approve and post!

## 📱 Telegram Bot Commands

- `/start` - Start the bot and see welcome message
- `/help` - Show help information
- `/cancel` - Cancel current operation

## 🔄 Workflow

1. **Topic Input**: User sends a topic via Telegram
2. **Content Crawling**: Bot crawls Medium, Quora, and Reddit for relevant content
3. **AI Processing**: Grok API generates:
   - Summarized post content
   - Image description for generation
4. **Image Generation**: Hugging Face API creates a custom image
5. **Preview**: User reviews content and image
6. **Platform Selection**: User chooses where to post
7. **Publishing**: Content is posted to selected platforms

## 📁 Project Structure

```
K-poster/
├── main.py                 # Main application entry point
├── telegram_bot.py         # Telegram bot handler
├── web_crawler.py          # Web scraping functionality
├── ai_content_generator.py # Grok API integration
├── image_generator.py      # AI image generation
├── social_media_poster.py  # Social media posting
├── config.py              # Configuration management
├── requirements.txt       # Python dependencies
├── .env.example          # Environment variables template
├── README.md             # This file
├── logs/                 # Application logs
├── generated_images/     # Generated images storage
└── temp/                 # Temporary files
```

## 🔧 Configuration

All configuration is managed through environment variables in the `.env` file:

- **Telegram**: Bot token
- **AI Services**: xAI Grok API key, Hugging Face token
- **Social Media**: Platform-specific API credentials
- **Application**: Debug mode, log level, crawling limits

## 🐛 Troubleshooting

### Common Issues

1. **"Missing required configuration"**
   - Ensure all required API keys are in your `.env` file
   - Check that `.env` file is in the project root

2. **Image generation fails**
   - Verify Hugging Face API token
   - Check if the model is available (may need to wait for model loading)

3. **Social media posting fails**
   - Verify API credentials and permissions
   - Check rate limits and API quotas

4. **Web crawling returns no results**
   - Try different topics or keywords
   - Check internet connection and site availability

### Logs

Check the `logs/k_poster.log` file for detailed error information.

## 🔒 Security Notes

- Keep your `.env` file secure and never commit it to version control
- Use environment-specific API keys for development and production
- Regularly rotate API keys and tokens
- Monitor API usage and costs

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the troubleshooting section above
- Review the logs for error details
- Open an issue on GitHub

## 🔮 Future Enhancements

- Support for more social media platforms (Twitter, TikTok)
- Advanced content scheduling
- Analytics and performance tracking
- Custom AI model fine-tuning
- Multi-language support
- Content templates and themes
