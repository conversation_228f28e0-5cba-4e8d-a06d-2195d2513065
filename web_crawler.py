import requests
import feedparser
from bs4 import BeautifulSoup
from typing import List, Dict, Optional
from loguru import logger
import time
import random
from urllib.parse import urljoin, urlparse
from config import settings

class WebCrawler:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
    def crawl_medium(self, topic: str, max_articles: int = 3) -> List[Dict]:
        """Crawl Medium articles for a given topic"""
        articles = []
        try:
            # Medium RSS feed search
            search_url = f"https://medium.com/feed/tag/{topic.replace(' ', '-').lower()}"
            
            feed = feedparser.parse(search_url)
            
            for entry in feed.entries[:max_articles]:
                article = {
                    'title': entry.title,
                    'url': entry.link,
                    'summary': entry.summary if hasattr(entry, 'summary') else '',
                    'source': 'Medium',
                    'published': entry.published if hasattr(entry, 'published') else ''
                }
                
                # Get full content
                content = self._get_article_content(entry.link)
                if content:
                    article['content'] = content
                    articles.append(article)
                
                time.sleep(random.uniform(1, 3))  # Rate limiting
                
        except Exception as e:
            logger.error(f"Error crawling Medium: {e}")
            
        return articles
    
    def crawl_quora(self, topic: str, max_articles: int = 3) -> List[Dict]:
        """Crawl Quora questions/answers for a given topic"""
        articles = []
        try:
            search_url = f"https://www.quora.com/search?q={topic.replace(' ', '+')}"
            
            response = self.session.get(search_url)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find question links
            question_links = soup.find_all('a', class_='q-box')[:max_articles]
            
            for link in question_links:
                href = link.get('href')
                if href:
                    full_url = urljoin('https://www.quora.com', href)
                    content = self._get_quora_content(full_url)
                    
                    if content:
                        articles.append({
                            'title': content.get('title', ''),
                            'url': full_url,
                            'content': content.get('content', ''),
                            'source': 'Quora',
                            'published': ''
                        })
                    
                    time.sleep(random.uniform(1, 3))
                    
        except Exception as e:
            logger.error(f"Error crawling Quora: {e}")
            
        return articles
    
    def crawl_reddit(self, topic: str, max_posts: int = 3) -> List[Dict]:
        """Crawl Reddit posts for a given topic"""
        articles = []
        try:
            # Reddit JSON API
            search_url = f"https://www.reddit.com/search.json?q={topic.replace(' ', '+')}&limit={max_posts}"
            
            response = self.session.get(search_url)
            data = response.json()
            
            for post in data['data']['children']:
                post_data = post['data']
                
                article = {
                    'title': post_data['title'],
                    'url': f"https://www.reddit.com{post_data['permalink']}",
                    'content': post_data.get('selftext', ''),
                    'source': 'Reddit',
                    'published': '',
                    'score': post_data.get('score', 0)
                }
                
                articles.append(article)
                time.sleep(random.uniform(0.5, 1.5))
                
        except Exception as e:
            logger.error(f"Error crawling Reddit: {e}")
            
        return articles
    
    def _get_article_content(self, url: str) -> Optional[str]:
        """Extract article content from URL"""
        try:
            response = self.session.get(url, timeout=10)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Try different content selectors
            content_selectors = [
                'article',
                '.post-content',
                '.entry-content',
                '.content',
                'main',
                '.story-body'
            ]
            
            content = ""
            for selector in content_selectors:
                elements = soup.select(selector)
                if elements:
                    content = ' '.join([elem.get_text().strip() for elem in elements])
                    break
            
            if not content:
                # Fallback to body text
                content = soup.get_text()
            
            # Clean and limit content
            content = ' '.join(content.split())
            return content[:2000]  # Limit to 2000 characters
            
        except Exception as e:
            logger.error(f"Error getting content from {url}: {e}")
            return None
    
    def _get_quora_content(self, url: str) -> Optional[Dict]:
        """Extract Quora question and answer content"""
        try:
            response = self.session.get(url, timeout=10)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract title
            title_elem = soup.find('h1') or soup.find('.question_text')
            title = title_elem.get_text().strip() if title_elem else ""
            
            # Extract answers
            answer_elements = soup.find_all('.answer_text') or soup.find_all('.ExpandedAnswer')
            answers = []
            
            for answer_elem in answer_elements[:2]:  # Get top 2 answers
                answer_text = answer_elem.get_text().strip()
                if answer_text:
                    answers.append(answer_text)
            
            content = f"Question: {title}\n\nAnswers:\n" + "\n\n".join(answers)
            
            return {
                'title': title,
                'content': content[:2000]  # Limit content
            }
            
        except Exception as e:
            logger.error(f"Error getting Quora content from {url}: {e}")
            return None
    
    def crawl_all_sources(self, topic: str) -> List[Dict]:
        """Crawl all sources for a given topic"""
        logger.info(f"Starting to crawl for topic: {topic}")
        
        all_articles = []
        
        # Crawl Medium
        logger.info("Crawling Medium...")
        medium_articles = self.crawl_medium(topic, max_articles=2)
        all_articles.extend(medium_articles)
        
        # Crawl Quora
        logger.info("Crawling Quora...")
        quora_articles = self.crawl_quora(topic, max_articles=2)
        all_articles.extend(quora_articles)
        
        # Crawl Reddit
        logger.info("Crawling Reddit...")
        reddit_articles = self.crawl_reddit(topic, max_posts=2)
        all_articles.extend(reddit_articles)
        
        logger.info(f"Crawled {len(all_articles)} articles total")
        return all_articles
