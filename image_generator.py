import requests
import io
import base64
from PIL import Image
from typing import Optional
from loguru import logger
from config import settings
import time

class ImageGenerator:
    def __init__(self):
        self.api_token = settings.huggingface_api_token
        self.model = settings.image_generation_model
        self.api_url = f"https://api-inference.huggingface.co/models/{self.model}"
        self.headers = {"Authorization": f"Bearer {self.api_token}"}
        
    def generate_image(self, description: str, retries: int = 3) -> Optional[bytes]:
        """Generate image using Hugging Face Inference API"""
        try:
            logger.info(f"Generating image with description: {description}")
            
            # Enhance the prompt for better results
            enhanced_prompt = self._enhance_prompt(description)
            
            payload = {
                "inputs": enhanced_prompt,
                "parameters": {
                    "negative_prompt": "text, watermark, signature, blurry, low quality, distorted",
                    "num_inference_steps": 20,
                    "guidance_scale": 7.5,
                    "width": 1024,
                    "height": 1024
                }
            }
            
            for attempt in range(retries):
                try:
                    response = requests.post(
                        self.api_url, 
                        headers=self.headers, 
                        json=payload,
                        timeout=60
                    )
                    
                    if response.status_code == 200:
                        image_bytes = response.content
                        
                        # Validate that we received an image
                        try:
                            Image.open(io.BytesIO(image_bytes))
                            logger.info("Image generated successfully")
                            return image_bytes
                        except Exception as e:
                            logger.error(f"Invalid image data received: {e}")
                            
                    elif response.status_code == 503:
                        # Model is loading, wait and retry
                        logger.warning(f"Model loading, waiting... (attempt {attempt + 1})")
                        time.sleep(20)
                        continue
                        
                    else:
                        logger.error(f"API error: {response.status_code} - {response.text}")
                        
                except requests.exceptions.Timeout:
                    logger.warning(f"Request timeout (attempt {attempt + 1})")
                    time.sleep(5)
                    
                except Exception as e:
                    logger.error(f"Error in image generation attempt {attempt + 1}: {e}")
                    time.sleep(5)
            
            # If all retries failed, try fallback model
            return self._generate_with_fallback(description)
            
        except Exception as e:
            logger.error(f"Error generating image: {e}")
            return None
    
    def _enhance_prompt(self, description: str) -> str:
        """Enhance the prompt for better image generation"""
        # Add quality and style modifiers
        quality_modifiers = [
            "high quality",
            "professional",
            "clean design",
            "modern style",
            "vibrant colors",
            "well-composed",
            "detailed"
        ]
        
        enhanced = f"{description}, {', '.join(quality_modifiers)}"
        return enhanced
    
    def _generate_with_fallback(self, description: str) -> Optional[bytes]:
        """Try with a fallback model if primary fails"""
        try:
            fallback_models = [
                "runwayml/stable-diffusion-v1-5",
                "CompVis/stable-diffusion-v1-4"
            ]
            
            for model in fallback_models:
                logger.info(f"Trying fallback model: {model}")
                fallback_url = f"https://api-inference.huggingface.co/models/{model}"
                
                payload = {"inputs": description}
                
                response = requests.post(
                    fallback_url,
                    headers=self.headers,
                    json=payload,
                    timeout=60
                )
                
                if response.status_code == 200:
                    try:
                        Image.open(io.BytesIO(response.content))
                        logger.info(f"Fallback model {model} succeeded")
                        return response.content
                    except:
                        continue
                        
                time.sleep(10)  # Wait between fallback attempts
                
        except Exception as e:
            logger.error(f"Fallback image generation failed: {e}")
            
        return None
    
    def resize_image_for_platform(self, image_bytes: bytes, platform: str) -> bytes:
        """Resize image for specific social media platform"""
        try:
            image = Image.open(io.BytesIO(image_bytes))
            
            # Platform-specific dimensions
            dimensions = {
                "instagram": (1080, 1080),  # Square
                "facebook": (1200, 630),    # Landscape
                "linkedin": (1200, 627),    # Landscape
                "twitter": (1200, 675),     # Landscape
                "default": (1024, 1024)     # Square
            }
            
            target_size = dimensions.get(platform, dimensions["default"])
            
            # Resize maintaining aspect ratio
            image.thumbnail(target_size, Image.Resampling.LANCZOS)
            
            # Create new image with target dimensions and paste resized image
            new_image = Image.new("RGB", target_size, (255, 255, 255))
            
            # Center the image
            x = (target_size[0] - image.width) // 2
            y = (target_size[1] - image.height) // 2
            new_image.paste(image, (x, y))
            
            # Convert back to bytes
            output = io.BytesIO()
            new_image.save(output, format="JPEG", quality=95)
            
            logger.info(f"Image resized for {platform}: {target_size}")
            return output.getvalue()
            
        except Exception as e:
            logger.error(f"Error resizing image for {platform}: {e}")
            return image_bytes  # Return original if resize fails
    
    def save_image(self, image_bytes: bytes, filename: str) -> str:
        """Save image to file and return path"""
        try:
            filepath = f"generated_images/{filename}"
            
            # Create directory if it doesn't exist
            import os
            os.makedirs("generated_images", exist_ok=True)
            
            with open(filepath, "wb") as f:
                f.write(image_bytes)
                
            logger.info(f"Image saved to {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Error saving image: {e}")
            return ""
    
    def create_preview_image(self, image_bytes: bytes) -> str:
        """Create a preview version of the image for Telegram"""
        try:
            # Resize to smaller size for preview
            preview_bytes = self.resize_image_for_platform(image_bytes, "preview")
            
            # Convert to base64 for easy transmission
            preview_b64 = base64.b64encode(preview_bytes).decode()
            
            return preview_b64
            
        except Exception as e:
            logger.error(f"Error creating preview: {e}")
            return ""
