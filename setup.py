#!/usr/bin/env python3
"""
Setup script for K-Poster application
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version}")
    return True

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    directories = ["logs", "generated_images", "temp"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")

def setup_env_file():
    """Setup environment file"""
    if not os.path.exists(".env"):
        if os.path.exists(".env.example"):
            import shutil
            shutil.copy(".env.example", ".env")
            print("✅ Created .env file from .env.example")
            print("⚠️  Please edit .env file with your API keys before running the application")
        else:
            print("❌ .env.example file not found")
            return False
    else:
        print("✅ .env file already exists")
    return True

def validate_env_file():
    """Basic validation of .env file"""
    if not os.path.exists(".env"):
        print("❌ .env file not found")
        return False
    
    required_vars = [
        "TELEGRAM_BOT_TOKEN",
        "XAI_API_KEY", 
        "HUGGINGFACE_API_TOKEN"
    ]
    
    missing_vars = []
    
    with open(".env", "r") as f:
        content = f.read()
        
    for var in required_vars:
        if f"{var}=" not in content or f"{var}=your_" in content:
            missing_vars.append(var)
    
    if missing_vars:
        print("⚠️  The following required environment variables need to be configured:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\nPlease edit your .env file with the actual API keys")
        return False
    
    print("✅ Environment variables configured")
    return True

def main():
    """Main setup function"""
    print("🚀 K-Poster Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Setup .env file
    if not setup_env_file():
        sys.exit(1)
    
    # Validate environment
    env_valid = validate_env_file()
    
    print("\n" + "=" * 50)
    print("🎉 Setup completed!")
    
    if env_valid:
        print("\n✅ Your K-Poster application is ready to run!")
        print("Start the application with: python main.py")
    else:
        print("\n⚠️  Please configure your .env file with API keys before running")
        print("Then start the application with: python main.py")
    
    print("\n📖 For detailed setup instructions, see README.md")

if __name__ == "__main__":
    main()
